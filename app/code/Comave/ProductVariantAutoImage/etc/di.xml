<?xml version="1.0" encoding="UTF-8"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <!-- Frontend and general image loading -->
    <type name="Magento\Catalog\Helper\Image">
        <plugin name="comave_product_variant_auto_image_url" 
                type="Comave\ProductVariantAutoImage\Plugin\ImageUrlProvider" 
                sortOrder="10" />
    </type>
    
    <!-- Admin grid image rendering -->
    <type name="Magento\Catalog\Block\Adminhtml\Product\Grid\Renderer\Image">
        <plugin name="comave_product_variant_auto_image_admin_grid" 
                type="Comave\ProductVariantAutoImage\Plugin\AdminGridImageRenderer" 
                sortOrder="10" />
    </type>
    
    <!-- Marketplace dashboard charts -->
    <type name="Webkul\Marketplace\Block\Account\Dashboard\ReviewChart">
        <plugin name="comave_product_variant_auto_image_marketplace_dashboard" 
                type="Comave\ProductVariantAutoImage\Plugin\MarketplaceDashboardImage" 
                sortOrder="10" />
    </type>
</config>
