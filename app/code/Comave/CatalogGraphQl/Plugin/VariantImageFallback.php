<?php
declare(strict_types=1);

namespace Comave\CatalogGraphQl\Plugin;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product;
use Magento\CatalogGraphQl\Model\Resolver\Product\ProductImage;


class VariantImageFallback
{
    private ProductRepositoryInterface $productRepository;

    public function __construct(ProductRepositoryInterface $productRepository)
    {
        $this->productRepository = $productRepository;
    }

    public function afterResolve(
        ProductImage $subject,
        $result,
        $field,
        $context,
        $info,
        array $value = null,
        array $args = null
    ) {
        // Check if this is a placeholder image
        if ($this->isPlaceholder($result)) {
            $parentImage = $this->getParentImage($value, $subject, $field, $context, $info, $args);
            if ($parentImage && !$this->isPlaceholder($parentImage)) {
                return $parentImage;
            }
        }

        return $result;
    }

    private function isPlaceholder(?array $imageData): bool
    {
        if (!$imageData || !isset($imageData['url'])) {
            return true;
        }

        $url = $imageData['url'];

        // Check for placeholder patterns in the URL
        return strpos($url, 'placeholder') !== false ||
               strpos($url, 'no_selection') !== false ||
               strpos($url, '/static/version') !== false ||
               strpos($url, 'Magento_Catalog/images/product/placeholder') !== false ||
               empty(trim($url));
    }

    private function getParentImage($value, $subject, $field, $context, $info, $args): ?array
    {
        if (!isset($value['model']) || !($value['model'] instanceof Product)) {
            return null;
        }

        $product = $value['model'];

        // Only apply to simple products that are part of configurable
        if ($product->getTypeId() !== 'simple') {
            return null;
        }

        try {
            $parentIds = $this->getParentIds($product);

            if (empty($parentIds)) {
                return null;
            }

            $parentProduct = $this->productRepository->getById($parentIds[0]);
            $parentValue = [
                'model' => $parentProduct,
                'sku' => $parentProduct->getSku(),
                'entity_id' => $parentProduct->getId()
            ];

            return $subject->resolve($field, $context, $info, $parentValue, $args);
        } catch (\Exception $e) {
            return null;
        }
    }

    private function getParentIds(Product $product): array
    {
        return $product->getTypeInstance()->getParentIdsByChild($product->getId());
    }
}
