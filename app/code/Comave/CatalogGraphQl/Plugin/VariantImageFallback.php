<?php
declare(strict_types=1);

namespace Comave\CatalogGraphQl\Plugin;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product;
use Magento\CatalogGraphQl\Model\Resolver\Product\ProductImage;

class VariantImageFallback
{
    private ProductRepositoryInterface $productRepository;

    public function __construct(ProductRepositoryInterface $productRepository)
    {
        $this->productRepository = $productRepository;
    }

    public function afterResolve(
        ProductImage $subject,
        $result,
        $field,
        $context,
        $info,
        array $value = null,
        array $args = null
    ) {
        // If result is placeholder, try parent image
        if ($this->isPlaceholder($result)) {
            $parentImage = $this->getParentImage($value, $subject, $field, $context, $info, $args);
            if ($parentImage && !$this->isPlaceholder($parentImage)) {
                return $parentImage;
            }
        }

        return $result;
    }

    private function isPlaceholder(?array $imageData): bool
    {
        return !$imageData || 
               !isset($imageData['url']) || 
               strpos($imageData['url'], 'placeholder') !== false;
    }

    private function getParentImage($value, $subject, $field, $context, $info, $args): ?array
    {
        if (!isset($value['model']) || !($value['model'] instanceof Product)) {
            return null;
        }

        $product = $value['model'];
        $parentIds = $product->getTypeInstance()->getParentIdsByChild($product->getId());
        
        if (empty($parentIds)) {
            return null;
        }

        try {
            $parentProduct = $this->productRepository->getById(reset($parentIds));
            $parentValue = ['model' => $parentProduct, 'sku' => $parentProduct->getSku()];
            
            return $subject->resolve($field, $context, $info, $parentValue, $args);
        } catch (\Exception $e) {
            return null;
        }
    }
}
