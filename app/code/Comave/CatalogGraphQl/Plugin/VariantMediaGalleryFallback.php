<?php
declare(strict_types=1);

namespace Comave\CatalogGraphQl\Plugin;

use Magento\Catalog\Api\ProductRepositoryInterface;
use Magento\Catalog\Model\Product;
use Magento\CatalogGraphQl\Model\Resolver\Product\MediaGallery;
use Magento\ConfigurableProduct\Model\Product\Type\Configurable;

class VariantMediaGalleryFallback
{
    private ProductRepositoryInterface $productRepository;

    public function __construct(ProductRepositoryInterface $productRepository)
    {
        $this->productRepository = $productRepository;
    }

    public function afterResolve(
        MediaGallery $subject,
        $result,
        $field,
        $context,
        $info,
        array $value = null,
        array $args = null
    ) {
        // If media gallery is empty, try parent
        if ($this->isMediaGalleryEmpty($result)) {
            $parentMediaGallery = $this->getParentMediaGallery($value, $subject, $field, $context, $info, $args);
            if (!$this->isMediaGalleryEmpty($parentMediaGallery)) {
                return $parentMediaGallery;
            }
        }

        return $result;
    }

    private function isMediaGalleryEmpty($mediaGallery): bool
    {
        return !$mediaGallery || empty($mediaGallery) || !is_array($mediaGallery);
    }

    private function getParentMediaGallery($value, $subject, $field, $context, $info, $args): ?array
    {
        if (!isset($value['model']) || !($value['model'] instanceof Product)) {
            return null;
        }

        $product = $value['model'];
        
        // Only apply to simple products that are part of configurable
        if ($product->getTypeId() !== 'simple') {
            return null;
        }

        try {
            $parentIds = $this->getParentIds($product);
            
            if (empty($parentIds)) {
                return null;
            }

            $parentProduct = $this->productRepository->getById($parentIds[0]);
            $parentValue = [
                'model' => $parentProduct,
                'sku' => $parentProduct->getSku(),
                'entity_id' => $parentProduct->getId()
            ];
            
            return $subject->resolve($field, $context, $info, $parentValue, $args);
        } catch (\Exception $e) {
            return null;
        }
    }

    private function getParentIds(Product $product): array
    {
        $configurableType = new Configurable();
        return $configurableType->getParentIdsByChild($product->getId());
    }
}
