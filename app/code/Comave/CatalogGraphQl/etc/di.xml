<?xml version="1.0"?>
<config xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
        xsi:noNamespaceSchemaLocation="urn:magento:framework:ObjectManager/etc/config.xsd">
    <type name="Magento\CatalogGraphQl\Model\Resolver\Products">
        <plugin name="apply_status_filter_plugin" type="Comave\CatalogGraphQl\Plugin\FilterEnabledProducts" />
    </type>
    <type name="Magento\CatalogGraphQl\Model\Resolver\Category\Products">
        <plugin name="removedisabled"
                type="Comave\CatalogGraphQl\Plugin\RemoveDisabledProducts"
                sortOrder="1"/>
    </type>
    <type name="Magento\CatalogGraphQl\Model\Resolver\Product\ProductImage">
        <plugin name="variant_image_fallback"
                type="Comave\CatalogGraphQl\Plugin\VariantImageFallback"
                sortOrder="10"/>
    </type>
    <type name="Magento\CatalogGraphQl\Model\Resolver\Product\MediaGallery">
        <plugin name="variant_media_gallery_fallback"
                type="Comave\CatalogGraphQl\Plugin\VariantMediaGalleryFallback"
                sortOrder="10"/>
    </type>
</config>
